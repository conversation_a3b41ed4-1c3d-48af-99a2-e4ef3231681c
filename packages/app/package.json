{"name": "rabble", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "dev": "NODE_ENV=development npx expo start", "prod": "NODE_ENV=production npx expo start", "update:dev": "NODE_ENV=development eas update", "update:prod": "NODE_ENV=production eas update", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "format": "prettier --write .", "lint": "eslint --fix --max-warnings=3 .", "precommit": "yarn format && yarn lint"}, "dependencies": {"@expo-google-fonts/montserrat": "^0.2.3", "@expo/vector-icons": "^14.0.0", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.1.1", "@likashefqet/react-native-image-zoom": "^4.3.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-community/slider": "4.5.6", "@react-native-masked-view/masked-view": "0.3.2", "@react-native/virtualized-lists": "0.76.3", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/material-top-tabs": "^6.6.6", "@react-navigation/native": "^6.1.6", "@react-navigation/stack": "^6.3.16", "@tanstack/react-query": "^4.0.0", "@trpc/client": "^10.44.1", "@trpc/react": "^9.27.4", "@trpc/react-query": "^10.44.1", "@uiw/react-markdown-preview": "^5.1.1", "axios": "^1.4.0", "clsx": "^1.2.1", "date-fns": "^2.30.0", "expo": "^53.0.17", "expo-application": "~6.1.5", "expo-blur": "~14.1.5", "expo-constants": "~17.1.7", "expo-contacts": "~14.2.5", "expo-device": "~7.1.4", "expo-file-system": "~18.1.11", "expo-font": "~13.3.2", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-location": "~18.1.6", "expo-print": "~14.1.4", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-tracking-transparency": "~5.2.4", "expo-updates": "~0.28.16", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lottie-react-native": "7.2.2", "lucide-react-native": "^0.321.0", "nativewind": "^2.0.11", "onesignal-expo-plugin": "^2.0.2", "rabble_be": "*", "react": "19.0.0", "react-hook-form": "^7.45.0", "react-native": "0.79.5", "react-native-autocomplete-dropdown": "^4.0.0-rc.5", "react-native-calendars": "^1.1310.0", "react-native-confetti-cannon": "^1.5.2", "react-native-expo-image-cache": "^4.1.0", "react-native-fbsdk-next": "^13.3.0", "react-native-gesture-handler": "~2.24.0", "react-native-markdown-package": "^1.8.2", "react-native-onesignal": "^5.0.5", "react-native-pager-view": "6.7.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-shimmer-placeholder": "^2.0.9", "react-native-svg": "15.11.2", "react-native-tab-view": "^3.5.2", "react-native-toast-message": "^2.1.6", "react-native-uuid": "^2.0.3", "react-native-webview": "13.13.5", "react-query": "^3.39.3", "rn-emoji-keyboard": "^1.7.0", "sentry-expo": "~7.0.0", "superjson": "1.13.3", "uuid": "^3.0.0", "yup": "^1.2.0", "zod": "^3.22.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/lodash": "^4.14.196", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "^4.1.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-config-universe": "^11.2.0", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^2.8.8", "tailwindcss": "3.3.2", "typescript": "~5.8.3"}, "private": true}